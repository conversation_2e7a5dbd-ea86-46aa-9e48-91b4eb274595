@echo off
echo ========================================
echo DataSync Module Status Check
echo ========================================
echo.

echo 1. Checking if module JAR exists...
if exist "C:\Honeywell\OptimizerSupervisor-N4.13.3.48\modules\datasync-wb.jar" (
    echo ✅ datasync-wb.jar found in modules directory
    dir "C:\Honeywell\OptimizerSupervisor-N4.13.3.48\modules\datasync-wb*.jar"
) else (
    echo ❌ datasync-wb.jar NOT found in modules directory
)
echo.

echo 2. Checking profiles directory...
if exist "C:\Users\<USER>\Niagara4.13\OptimizerSupervisor\shared\datasync" (
    echo ✅ DataSync shared directory exists
    if exist "C:\Users\<USER>\Niagara4.13\OptimizerSupervisor\shared\datasync\profiles" (
        echo ✅ Profiles directory exists
        echo Contents:
        dir "C:\Users\<USER>\Niagara4.13\OptimizerSupervisor\shared\datasync\profiles"
    ) else (
        echo ❌ Profiles directory does NOT exist
    )
) else (
    echo ❌ DataSync shared directory does NOT exist
)
echo.

echo 3. Checking Niagara installation...
if exist "C:\Honeywell\OptimizerSupervisor-N4.13.3.48\bin\workbench.exe" (
    echo ✅ Niagara Workbench found
) else (
    echo ❌ Niagara Workbench NOT found
)
echo.

echo 4. Checking build output...
if exist "datasync\datasync-wb\build\libs\datasync-wb.jar" (
    echo ✅ Build output exists
    dir "datasync\datasync-wb\build\libs\datasync-wb.jar"
) else (
    echo ❌ Build output does NOT exist
)
echo.

echo ========================================
echo Status check complete
echo ========================================
echo.
echo Next steps:
echo 1. If module JAR is missing, run: gradlew :datasync-wb:jar
echo 2. If profiles directory is missing, it will be created automatically
echo 3. Check Niagara Workbench Tools menu for DataSync
echo.
pause
